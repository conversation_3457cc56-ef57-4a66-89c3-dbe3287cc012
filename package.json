{"name": "@aojdevstudio/cdev", "version": "0.0.20", "description": "Claude Development - Parallel workflow system with intelligent hooks, Linear integration, and automated agent management", "main": "src/installer.js", "bin": {"cdev": "bin/cli.js"}, "scripts": {"test": "npm run test:unit && npm run test:integration", "test:unit": "jest --config config/jest.config.unit.js", "test:integration": "jest --config config/jest.config.integration.js", "test:dom": "jest --config config/jest.config.dom.js", "test:all": "npm run test:unit && npm run test:integration && npm run test:dom", "test:watch": "jest --watch", "test:ci": "npm run test:all", "test:coverage": "jest --config config/jest.config.coverage.js", "lint": "eslint src test --ext .js", "lint:fix": "eslint src test --ext .js --fix", "format": "prettier --write \"**/*.{js,json,md,yml,yaml}\"", "format:check": "prettier --check \"**/*.{js,json,md,yml,yaml}\"", "lint:prettier": "prettier --check \"**/*.{js,json,md,yml,yaml}\"", "quality": "npm run lint && npm run format:check", "quality:fix": "npm run lint:fix && npm run format", "build": "npm run quality && chmod +x scripts/python/*.py && chmod +x scripts/changelog/*.js && chmod +x bin/cli.js", "prepublishOnly": "npm run security:check && npm run build", "security:check": "./scripts/python/security-check.py", "security:audit": "npm audit --audit-level moderate", "postpublish": "./scripts/python/postpublish.py --skip-verification || true", "prepare": "./scripts/python/prepublish.py", "lint:python": "ruff check scripts/python/", "lint:python:fix": "ruff check scripts/python/ --fix", "format:python": "ruff format scripts/python/", "format:python:check": "ruff format scripts/python/ --check", "quality:python": "npm run lint:python && npm run format:python:check", "quality:python:fix": "npm run lint:python:fix && npm run format:python", "typecheck": "tsc --noEmit", "changelog:update": "node scripts/changelog/update-changelog.js", "changelog:auto": "node scripts/changelog/update-changelog.js --auto", "changelog:manual": "node scripts/changelog/update-changelog.js --manual", "changelog:preview": "node scripts/changelog/update-changelog.js --auto --dry-run", "changelog:force": "node scripts/changelog/update-changelog.js --auto --force"}, "keywords": ["claude", "parallel", "development", "git", "worktree", "linear", "automation", "cli", "npx", "workflow"], "author": "AOJDevStudio <<EMAIL>>", "license": "CC-BY-NC-SA-4.0", "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/AOJDevStudio/cdev.git"}, "bugs": {"url": "https://github.com/AOJDevStudio/cdev/issues"}, "homepage": "https://github.com/AOJDevStudio/cdev#readme", "files": ["bin/", "src/**/*.js", "!src/**/*.test.js", "!src/**/*.spec.js", "scripts/python/", "scripts/wrappers/", "scripts/changelog/", ".claude/", ".claude/**/*", ".claude/agents/", ".claude/agents/**/*.md", ".claude/hooks/", ".claude/hooks/**/*", "ai-docs/", "README.md", "LICENSE", "CHANGELOG.md", ".env.example"], "dependencies": {"chalk": "^4.1.2", "commander": "^11.0.0", "dotenv": "^16.6.1", "fs-extra": "11.3.0", "inquirer": "^8.2.6", "ora": "^5.4.1", "semver": "^7.5.4", "which": "^3.0.1"}, "devDependencies": {"@babel/core": "7.28.0", "@babel/preset-env": "7.28.0", "@testing-library/jest-dom": "^6.1.4", "@types/jest": "29.5.14", "babel-jest": "30.0.4", "eslint": "^8.50.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jest": "^27.4.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-unused-imports": "4.1.4", "jest": "^29.7.0", "jest-environment-jsdom": "30.0.4", "prettier": "^3.0.3", "ts-jest": "29.4.0", "typescript": "5.8.3"}, "preferGlobal": true, "publishConfig": {"access": "public"}}